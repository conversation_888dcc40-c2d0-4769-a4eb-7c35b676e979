'use client';

import { useEffect } from 'react';
import PWAInstallButton from '@/components/PWAInstallButton';
import PWANotificationHandler from '@/components/PWANotificationHandler';

interface PWAProviderProps {
  children: React.ReactNode;
}

export default function PWAProvider({ children }: PWAProviderProps) {
  useEffect(() => {
    // Register service worker
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('SW registered: ', registration);
        })
        .catch((registrationError) => {
          console.log('SW registration failed: ', registrationError);
        });
    }
  }, []);

  return (
    <>
      {children}
      <PWAInstallButton />
      <PWANotificationHandler />
    </>
  );
} 