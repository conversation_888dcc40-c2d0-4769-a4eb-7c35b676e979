'use client'

import { useState, useEffect, useCallback } from 'react'
import { useSession } from 'next-auth/react'
import { 
  getPWASettings, 
  updatePWASetting,
  getAllPushSubscriptions,
  getNotificationCampaigns,
  createNotificationCampaign,
  deleteNotificationCampaign,
  getPushSubscriptionStats,
  getNotificationCampaignStats,
  subscribeToPushSubscriptionChanges,
  subscribeToNotificationCampaignChanges
} from '@/lib/database'
import type { PWASetting, PushSubscription, NotificationCampaign } from '@/lib/database'
import AdminLayout from '@/components/AdminLayout'

interface Stats {
  subscriptions: {
    total_subscriptions: number
    active_subscriptions: number
    inactive_subscriptions: number
    subscriptions_today: number
  }
  campaigns: {
    total_campaigns: number
    active_campaigns: number
    total_sent: number
    total_delivered: number
    total_clicked: number
    avg_delivery_rate: number
    avg_click_rate: number
  }
}

type TargetType = 'all' | 'role' | 'specific' | 'active'

export default function PWAManagementPage() {
  const { data: session } = useSession()
  
  // State management
  const [activeTab, setActiveTab] = useState('overview')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [successMessage, setSuccessMessage] = useState('')
  
  // Data state
  const [pwaSettings, setPwaSettings] = useState<PWASetting[]>([])
  const [pushSubscriptions, setPushSubscriptions] = useState<PushSubscription[]>([])
  const [campaigns, setCampaigns] = useState<NotificationCampaign[]>([])
  const [stats, setStats] = useState<Stats>({
    subscriptions: {
      total_subscriptions: 0,
      active_subscriptions: 0,
      inactive_subscriptions: 0,
      subscriptions_today: 0
    },
    campaigns: {
      total_campaigns: 0,
      active_campaigns: 0,
      total_sent: 0,
      total_delivered: 0,
      total_clicked: 0,
      avg_delivery_rate: 0,
      avg_click_rate: 0
    }
  })

  // Campaign form state
  const [newCampaign, setNewCampaign] = useState({
    name: '',
    title: '',
    body: '',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    image: '',
    url: '',
    target_type: 'all' as TargetType,
    target_criteria: {},
    scheduled_at: ''
  })

  const loadData = useCallback(async () => {
    try {
      setIsLoading(true)
      await Promise.all([
        loadSettings(),
        loadSubscriptions(),
        loadCampaigns(),
        loadStats()
      ])
    } catch {
      setError('Failed to load PWA data')
    } finally {
      setIsLoading(false)
    }
  }, [])

  useEffect(() => {
    if (session?.user.role === 'admin') {
      loadData()
      
      // Set up real-time subscriptions
      const subscriptionsSub = subscribeToPushSubscriptionChanges((payload) => {
        if (payload.eventType === 'INSERT') {
          setPushSubscriptions(prev => [payload.new, ...prev])
        } else if (payload.eventType === 'DELETE') {
          setPushSubscriptions(prev => prev.filter(sub => sub.id !== payload.old.id))
        }
        loadStats() // Refresh stats
      })

      const campaignsSub = subscribeToNotificationCampaignChanges((payload) => {
        if (payload.eventType === 'INSERT') {
          setCampaigns(prev => [payload.new, ...prev])
        } else if (payload.eventType === 'UPDATE') {
          setCampaigns(prev => prev.map(campaign => 
            campaign.id === payload.new.id ? payload.new : campaign
          ))
        } else if (payload.eventType === 'DELETE') {
          setCampaigns(prev => prev.filter(campaign => campaign.id !== payload.old.id))
        }
        loadStats() // Refresh stats
      })

      return () => {
        subscriptionsSub.unsubscribe()
        campaignsSub.unsubscribe()
      }
    }
  }, [session, loadData])

  const loadSettings = async () => {
    const { data, error } = await getPWASettings()
    if (error) {
      setError('Failed to load PWA settings')
      return
    }
    setPwaSettings(data || [])
  }

  const loadSubscriptions = async () => {
    const { data, error } = await getAllPushSubscriptions()
    if (error) {
      setError('Failed to load push subscriptions')
      return
    }
    setPushSubscriptions(data || [])
  }

  const loadCampaigns = async () => {
    const { data, error } = await getNotificationCampaigns()
    if (error) {
      setError('Failed to load campaigns')
      return
    }
    setCampaigns(data || [])
  }

  const loadStats = async () => {
    try {
      const [subscriptionStats, campaignStats] = await Promise.all([
        getPushSubscriptionStats(),
        getNotificationCampaignStats()
      ])

      setStats({
        subscriptions: subscriptionStats.data || {
          total_subscriptions: 0,
          active_subscriptions: 0,
          inactive_subscriptions: 0,
          subscriptions_today: 0
        },
        campaigns: campaignStats.data || {
          total_campaigns: 0,
          active_campaigns: 0,
          total_sent: 0,
          total_delivered: 0,
          total_clicked: 0,
          avg_delivery_rate: 0,
          avg_click_rate: 0
        }
      })
    } catch {
      console.error('Failed to load stats')
    }
  }

  const handleSettingUpdate = async (key: string, value: string | boolean) => {
    try {
      const { error } = await updatePWASetting(key, value)
      if (error) {
        setError('Failed to update setting')
        return
      }
      
      // Update local state
      setPwaSettings(settings => 
        settings.map(setting => 
          setting.key === key ? { ...setting, value } : setting
        )
      )
      setSuccessMessage('Setting updated successfully')
      setTimeout(() => setSuccessMessage(''), 3000)
    } catch {
      setError('An error occurred while updating setting')
    }
  }

  const handleCreateCampaign = async () => {
    try {
      if (!newCampaign.name || !newCampaign.title || !newCampaign.body) {
        setError('Please fill in all required fields')
        return
      }

      const { data, error } = await createNotificationCampaign({
        ...newCampaign,
        created_by: session!.user.id,
        actions: [] // Default empty actions
      })

      if (error) {
        setError('Failed to create campaign')
        return
      }

      setCampaigns(prev => [data, ...prev])
      setNewCampaign({
        name: '',
        title: '',
        body: '',
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-72x72.png',
        image: '',
        url: '',
        target_type: 'all',
        target_criteria: {},
        scheduled_at: ''
      })
      setSuccessMessage('Campaign created successfully')
      setTimeout(() => setSuccessMessage(''), 3000)
    } catch {
      setError('An error occurred while creating campaign')
    }
  }

  const handleDeleteCampaign = async (campaignId: string) => {
    if (!confirm('Are you sure you want to delete this campaign?')) return

    try {
      const { error } = await deleteNotificationCampaign(campaignId)
      if (error) {
        setError('Failed to delete campaign')
        return
      }

      setCampaigns(prev => prev.filter(campaign => campaign.id !== campaignId))
      setSuccessMessage('Campaign deleted successfully')
      setTimeout(() => setSuccessMessage(''), 3000)
    } catch {
      setError('An error occurred while deleting campaign')
    }
  }

  const sendTestNotification = async () => {
    try {
      const response = await fetch('/api/admin/push-notification/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: 'Test Notification',
          body: 'This is a test notification from the admin panel',
          icon: '/icons/icon-192x192.png'
        })
      })

      if (!response.ok) {
        throw new Error('Failed to send test notification')
      }

      setSuccessMessage('Test notification sent successfully')
      setTimeout(() => setSuccessMessage(''), 3000)
    } catch {
      setError('Failed to send test notification')
    }
  }

  if (isLoading) {
    return (
      <AdminLayout title="PWA Management" description="Manage push notifications, campaigns, and PWA settings">
        <div className="flex items-center justify-center py-12">
          <div className="text-lg">Loading PWA data...</div>
        </div>
      </AdminLayout>
    )
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'campaigns', name: 'Campaigns', icon: '📢' },
    { id: 'subscriptions', name: 'Subscriptions', icon: '👥' },
    { id: 'settings', name: 'PWA Settings', icon: '⚙️' },
    { id: 'analytics', name: 'Analytics', icon: '📈' }
  ]

  return (
    <AdminLayout title="PWA Management" description="Manage push notifications, campaigns, and PWA settings">
      {error && (
        <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-800">{error}</p>
          <button 
            onClick={() => setError('')}
            className="mt-2 text-sm text-red-600 hover:text-red-800"
          >
            Dismiss
          </button>
        </div>
      )}

      {successMessage && (
        <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
          <p className="text-green-800">{successMessage}</p>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.icon} {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Stats Cards */}
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="text-2xl">👥</div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Subscriptions</p>
                <p className="text-2xl font-bold text-gray-900">{stats.subscriptions.total_subscriptions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="text-2xl">✅</div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Subscriptions</p>
                <p className="text-2xl font-bold text-green-600">{stats.subscriptions.active_subscriptions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="text-2xl">📢</div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Campaigns</p>
                <p className="text-2xl font-bold text-blue-600">{stats.campaigns.total_campaigns}</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <div className="text-2xl">📨</div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Notifications Sent</p>
                <p className="text-2xl font-bold text-purple-600">{stats.campaigns.total_sent}</p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white p-6 rounded-lg shadow col-span-full">
            <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
            <div className="flex flex-wrap gap-4">
              <button
                onClick={sendTestNotification}
                className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                🧪 Send Test Notification
              </button>
              <button
                onClick={() => setActiveTab('campaigns')}
                className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
              >
                ➕ Create Campaign
              </button>
              <button
                onClick={() => setActiveTab('settings')}
                className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors"
              >
                ⚙️ PWA Settings
              </button>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'campaigns' && (
        <div className="space-y-6">
          {/* Create New Campaign */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold mb-4">Create New Campaign</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label htmlFor="campaign-name" className="block text-sm font-medium text-gray-700 mb-2">
                  Campaign Name *
                </label>
                <input
                  id="campaign-name"
                  type="text"
                  value={newCampaign.name}
                  onChange={(e) => setNewCampaign(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="Enter campaign name"
                />
              </div>

              <div>
                <label htmlFor="target-audience" className="block text-sm font-medium text-gray-700 mb-2">
                  Target Audience
                </label>
                <select
                  id="target-audience"
                  value={newCampaign.target_type}
                  onChange={(e) => setNewCampaign(prev => ({ ...prev, target_type: e.target.value as TargetType }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                >
                  <option value="all">All Users</option>
                  <option value="role">By Role</option>
                  <option value="active">Active Users Only</option>
                  <option value="specific">Specific Users</option>
                </select>
              </div>

              <div className="md:col-span-2">
                <label htmlFor="notification-title" className="block text-sm font-medium text-gray-700 mb-2">
                  Notification Title *
                </label>
                <input
                  id="notification-title"
                  type="text"
                  value={newCampaign.title}
                  onChange={(e) => setNewCampaign(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="Enter notification title"
                />
              </div>

              <div className="md:col-span-2">
                <label htmlFor="notification-body" className="block text-sm font-medium text-gray-700 mb-2">
                  Notification Body *
                </label>
                <textarea
                  id="notification-body"
                  value={newCampaign.body}
                  onChange={(e) => setNewCampaign(prev => ({ ...prev, body: e.target.value }))}
                  rows={3}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="Enter notification message"
                />
              </div>

              <div>
                <label htmlFor="icon-url" className="block text-sm font-medium text-gray-700 mb-2">
                  Icon URL
                </label>
                <input
                  id="icon-url"
                  type="text"
                  value={newCampaign.icon}
                  onChange={(e) => setNewCampaign(prev => ({ ...prev, icon: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="/icons/icon-192x192.png"
                />
              </div>

              <div>
                <label htmlFor="action-url" className="block text-sm font-medium text-gray-700 mb-2">
                  Action URL (Optional)
                </label>
                <input
                  id="action-url"
                  type="text"
                  value={newCampaign.url}
                  onChange={(e) => setNewCampaign(prev => ({ ...prev, url: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="https://yoursite.com/page"
                />
              </div>
            </div>

            <div className="mt-6 flex space-x-4">
              <button
                onClick={handleCreateCampaign}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
              >
                Create Campaign
              </button>
              <button
                onClick={() => setNewCampaign({
                  name: '',
                  title: '',
                  body: '',
                  icon: '/icons/icon-192x192.png',
                  badge: '/icons/icon-72x72.png',
                  image: '',
                  url: '',
                  target_type: 'all',
                  target_criteria: {},
                  scheduled_at: ''
                })}
                className="bg-gray-600 text-white px-6 py-2 rounded-md hover:bg-gray-700 transition-colors"
              >
                Reset Form
              </button>
            </div>
          </div>

          {/* Existing Campaigns */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">Campaign History</h3>
            </div>
            <div className="p-6">
              {campaigns.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No campaigns created yet</p>
              ) : (
                <div className="space-y-4">
                  {campaigns.map((campaign) => (
                    <div key={campaign.id} className="border border-gray-200 rounded-md p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{campaign.name}</h4>
                          <p className="text-sm text-gray-600">{campaign.title}</p>
                          <p className="text-xs text-gray-500 mt-1">
                            Status: <span className={`font-medium ${
                              campaign.status === 'sent' ? 'text-green-600' :
                              campaign.status === 'scheduled' ? 'text-blue-600' :
                              campaign.status === 'sending' ? 'text-yellow-600' :
                              'text-gray-600'
                            }`}>{campaign.status}</span>
                          </p>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>Sent: {campaign.total_sent || 0}</span>
                          <span>Delivered: {campaign.total_delivered || 0}</span>
                          <span>Clicked: {campaign.total_clicked || 0}</span>
                          <button
                            onClick={() => handleDeleteCampaign(campaign.id)}
                            className="text-red-600 hover:text-red-800"
                          >
                            🗑️
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'subscriptions' && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold">Push Notification Subscriptions</h3>
            <p className="text-sm text-gray-600">Manage user push notification subscriptions</p>
          </div>
          <div className="p-6">
            {pushSubscriptions.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📱</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Push Subscriptions Yet</h3>
                <p className="text-gray-500 mb-6 max-w-md mx-auto">
                  Users need to enable notifications first. They&apos;ll see a banner prompting them to subscribe when they visit the app.
                </p>
                <div className="space-y-2 text-sm text-gray-600">
                  <p>📍 <strong>How users subscribe:</strong></p>
                  <p>1. Visit the app on mobile: <code className="bg-gray-100 px-2 py-1 rounded">http://************:3001</code></p>
                  <p>2. Click &quot;Enable&quot; on the notification banner</p>
                  <p>3. Allow notifications when prompted</p>
                  <p>4. Subscriptions will appear here automatically</p>
                </div>
                <div className="mt-6 space-y-2">
                  <div>
                    <a
                      href="/push-test"
                      className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
                    >
                      🧪 Open Push Notification Test Page →
                    </a>
                  </div>
                  <div>
                    <a
                      href="/profile"
                      className="inline-flex items-center text-gray-600 hover:text-gray-700 text-sm"
                    >
                      🔔 Test on profile page →
                    </a>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {pushSubscriptions.map((subscription) => {
                  // Get user data - works for both demo and production subscriptions
                                     const userName = (subscription as any).user?.name || 
                     (subscription as any).user?.email || 
                     (subscription.user_id === 'demo-admin-123' ? 'Demo Admin' :
                      subscription.user_id === 'demo-user-456' ? 'Demo User' :
                      subscription.user_id === 'demo-mod-789' ? 'Demo Moderator' :
                      subscription.user_id === 'anonymous-visitor' ? 'Anonymous Visitor' :
                      subscription.user_id?.startsWith('anonymous-') ? 'Anonymous User' :
                      `User ${subscription.user_id?.slice(-3) || 'Unknown'}`)
                  
                  const userRole = (subscription as any).user?.role ||
                    (subscription.user_id === 'demo-admin-123' ? 'admin' :
                     subscription.user_id === 'demo-user-456' ? 'user' :
                     subscription.user_id === 'demo-mod-789' ? 'moderator' :
                     'user')
                  
                  const isDemo = subscription.id.startsWith('demo-')
                  
                  return (
                    <div key={subscription.id} className="border border-gray-200 rounded-md p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">
                            {userName}
                            {isDemo && <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">DEMO</span>}
                          </p>
                          <p className="text-sm text-gray-600">
                            Role: {userRole}
                          </p>
                          <p className="text-xs text-gray-500">
                            Subscribed: {new Date(subscription.created_at).toLocaleDateString()}
                          </p>
                          <p className="text-xs text-gray-400">
                            Device: {subscription.user_agent || 'Unknown'}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            subscription.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {subscription.is_active ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </div>
        </div>
      )}

      {activeTab === 'settings' && (
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold">PWA Settings</h3>
            <p className="text-sm text-gray-600">Configure push notification and PWA behavior</p>
          </div>
          <div className="p-6">
            <div className="space-y-6">
              {/* Install Button Controls */}
              <div className="border-b border-gray-200 pb-6">
                <h4 className="text-md font-semibold mb-4 text-blue-600">📱 Install Button Settings</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Enable Install Button
                    </label>
                    <select
                      value={pwaSettings.find(s => s.key === 'pwa_install_button_enabled')?.value?.toString() || 'true'}
                      onChange={(e) => handleSettingUpdate('pwa_install_button_enabled', e.target.value === 'true')}
                      className="w-full border border-gray-300 rounded-md px-3 py-2"
                    >
                      <option value="true">Enabled</option>
                      <option value="false">Disabled</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Button Position
                    </label>
                    <select
                      value={pwaSettings.find(s => s.key === 'pwa_install_button_position')?.value?.toString() || 'bottom-right'}
                      onChange={(e) => handleSettingUpdate('pwa_install_button_position', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2"
                    >
                      <option value="bottom-right">Bottom Right</option>
                      <option value="bottom-left">Bottom Left</option>
                      <option value="top-right">Top Right</option>
                      <option value="top-left">Top Left</option>
                      <option value="bottom-center">Bottom Center</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Button Style
                    </label>
                    <select
                      value={pwaSettings.find(s => s.key === 'pwa_install_button_style')?.value?.toString() || 'floating'}
                      onChange={(e) => handleSettingUpdate('pwa_install_button_style', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2"
                    >
                      <option value="floating">Floating (Round)</option>
                      <option value="minimal">Minimal (Clean)</option>
                      <option value="gradient">Gradient (Colorful)</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Button Text
                    </label>
                    <input
                      type="text"
                      value={pwaSettings.find(s => s.key === 'pwa_install_button_text')?.value?.toString() || 'Install App'}
                      onChange={(e) => handleSettingUpdate('pwa_install_button_text', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2"
                      placeholder="Install App"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Button Icon
                    </label>
                    <input
                      type="text"
                      value={pwaSettings.find(s => s.key === 'pwa_install_button_icon')?.value?.toString() || '📱'}
                      onChange={(e) => handleSettingUpdate('pwa_install_button_icon', e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2"
                      placeholder="📱"
                    />
                  </div>
                </div>
              </div>
              
              {/* Existing PWA settings */}
              {pwaSettings.filter(setting => !setting.key.startsWith('pwa_install_button')).map((setting) => (
                <div key={setting.key} className="space-y-2">
                  <label htmlFor={`setting-${setting.key}`} className="block text-sm font-medium text-gray-700">
                    {setting.key.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                  </label>
                  {typeof setting.value === 'boolean' ? (
                    <select
                      id={`setting-${setting.key}`}
                      value={setting.value.toString()}
                      onChange={(e) => handleSettingUpdate(setting.key, e.target.value === 'true')}
                      className="w-full border border-gray-300 rounded-md px-3 py-2"
                    >
                      <option value="true">Enabled</option>
                      <option value="false">Disabled</option>
                    </select>
                  ) : (
                    <input
                      id={`setting-${setting.key}`}
                      type="text"
                      value={setting.value as string}
                      onChange={(e) => handleSettingUpdate(setting.key, e.target.value)}
                      className="w-full border border-gray-300 rounded-md px-3 py-2"
                    />
                  )}
                  {setting.description && (
                    <p className="text-xs text-gray-500">{setting.description}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'analytics' && (
        <div className="space-y-6">
          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <h4 className="text-sm font-medium text-gray-600 mb-2">Delivery Rate</h4>
              <p className="text-2xl font-bold text-blue-600">
                {stats.campaigns.avg_delivery_rate.toFixed(1)}%
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <h4 className="text-sm font-medium text-gray-600 mb-2">Click Rate</h4>
              <p className="text-2xl font-bold text-green-600">
                {stats.campaigns.avg_click_rate.toFixed(1)}%
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow">
              <h4 className="text-sm font-medium text-gray-600 mb-2">New Subscriptions Today</h4>
              <p className="text-2xl font-bold text-purple-600">
                {stats.subscriptions.subscriptions_today}
              </p>
            </div>
          </div>

          {/* Detailed Analytics */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold">Analytics Dashboard</h3>
            </div>
            <div className="p-6">
              <p className="text-gray-500 text-center py-8">
                Advanced analytics charts and reports will be displayed here.
                <br />
                Integration with charting libraries like Chart.js or Recharts recommended.
              </p>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  )
} 