'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

interface NotificationPermissionProps {
  className?: string
  showBanner?: boolean
}

export default function NotificationPermission({ className = '', showBanner = true }: NotificationPermissionProps) {
  const { data: session } = useSession()
  const [mounted, setMounted] = useState(false)
  const [permission, setPermission] = useState<NotificationPermission>('default')
  const [isSubscribed, setIsSubscribed] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [showPrompt, setShowPrompt] = useState(false)

  useEffect(() => {
    setMounted(true)
    checkNotificationStatus()
  }, []) // Remove session dependency - should work for all users

  useEffect(() => {
    // Re-check when session changes
    if (mounted) {
      checkNotificationStatus()
    }
  }, [session, mounted])

  const checkNotificationStatus = async () => {
    if (typeof window === 'undefined' || !('Notification' in window)) {
      return
    }

    setPermission(Notification.permission)
    
    // Check if already subscribed
    if ('serviceWorker' in navigator && Notification.permission === 'granted') {
      try {
        const registration = await navigator.serviceWorker.getRegistration()
        if (registration) {
          const subscription = await registration.pushManager.getSubscription()
          setIsSubscribed(!!subscription)
        }
      } catch (error) {
        console.error('Error checking subscription status:', error)
      }
    }

    // Show prompt if user hasn't been asked (NO LOGIN REQUIRED!)
    if (Notification.permission === 'default' && showBanner) {
      setTimeout(() => setShowPrompt(true), 2000) // Show after 2 seconds
    }
  }

  const requestPermission = async () => {
    try {
      setIsLoading(true)
      setError('')

      if (!('Notification' in window)) {
        setError('Notifications are not supported in this browser')
        return
      }

      // Allow anonymous subscriptions for PWA experience
      // if (!session) {
      //   setError('Please log in first')
      //   return
      // }

      // Request permission
      const permission = await Notification.requestPermission()
      setPermission(permission)

      if (permission !== 'granted') {
        setError('Notification permission denied')
        return
      }

      // Register service worker
      let registration = await navigator.serviceWorker.getRegistration()
      if (!registration) {
        registration = await navigator.serviceWorker.register('/sw.js')
      }

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready

      // Check if already subscribed
      const existingSubscription = await registration.pushManager.getSubscription()
      if (existingSubscription) {
        setIsSubscribed(true)
        setShowPrompt(false)
        return
      }

      // Get VAPID public key from settings
      const vapidResponse = await fetch('/api/pwa/vapid-key')
      const vapidData = await vapidResponse.json()
      
      if (!vapidData.publicKey || vapidData.publicKey === '""') {
        setError('VAPID keys not configured. Please ask an admin to set up push notification keys.')
        return
      }

      // Create new subscription with proper VAPID key
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlB64ToUint8Array(vapidData.publicKey)
      })

      // Save subscription to server
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          subscription: {
            endpoint: subscription.endpoint,
            keys: {
              p256dh: btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('p256dh')!))),
              auth: btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('auth')!)))
            }
          },
          userAgent: navigator.userAgent
        })
      })

      const result = await response.json()
      
      if (!response.ok) {
        throw new Error(result.error || 'Failed to save subscription')
      }

      setIsSubscribed(true)
      setShowPrompt(false)

      // Show success notification
      new Notification('🎉 Notifications Enabled!', {
        body: 'You will now receive push notifications from this app.',
        icon: '/icons/icon-192x192.png'
      })

      console.log('✅ Push notification subscription successful:', result)

    } catch (error) {
      console.error('❌ Notification permission error:', error)
      setError(error instanceof Error ? error.message : 'Failed to enable notifications')
    } finally {
      setIsLoading(false)
    }
  }

  const unsubscribe = async () => {
    try {
      setIsLoading(true)
      
      const registration = await navigator.serviceWorker.getRegistration()
      if (registration) {
        const subscription = await registration.pushManager.getSubscription()
        if (subscription) {
          await subscription.unsubscribe()
          
          // Notify server
          await fetch('/api/push/subscribe', {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ endpoint: subscription.endpoint })
          })
        }
      }
      
      setIsSubscribed(false)
      console.log('✅ Unsubscribed from push notifications')
    } catch (error) {
      console.error('❌ Unsubscribe error:', error)
      setError('Failed to unsubscribe')
    } finally {
      setIsLoading(false)
    }
  }

  // Helper function to convert VAPID key
  function urlB64ToUint8Array(base64String: string) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/')
    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)
    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }

  if (!mounted || !('Notification' in window)) {
    return null
  }

  // Banner prompt
  if (showPrompt && permission === 'default') {
    return (
      <div className={`fixed top-0 left-0 right-0 bg-blue-600 text-white p-4 z-50 ${className}`}>
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-xl mr-3">🔔</span>
            <div>
              <p className="font-medium">Stay Updated!</p>
              <p className="text-sm opacity-90">Enable notifications to receive important updates.</p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={requestPermission}
              disabled={isLoading}
              className="bg-white text-blue-600 px-4 py-2 rounded-md font-medium hover:bg-gray-100 disabled:opacity-50"
            >
              {isLoading ? 'Enabling...' : 'Enable'}
            </button>
            <button
              onClick={() => setShowPrompt(false)}
              className="text-white/80 hover:text-white"
            >
              ✕
            </button>
          </div>
        </div>
        {error && (
          <div className="mt-2 text-sm text-red-200">
            {error}
          </div>
        )}
      </div>
    )
  }

  // Status component (for settings pages, etc.)
  if (!showBanner) {
    return (
      <div className={`bg-white rounded-lg border p-4 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="text-2xl mr-3">
              {permission === 'granted' ? '🔔' : '🔕'}
            </span>
            <div>
              <h3 className="font-medium">Push Notifications</h3>
              <p className="text-sm text-gray-600">
                {permission === 'granted' && isSubscribed ? 'Enabled and active' :
                 permission === 'granted' ? 'Permission granted' :
                 permission === 'denied' ? 'Permission denied' :
                 'Not enabled'}
              </p>
            </div>
          </div>
          <div className="flex space-x-2">
            {permission !== 'granted' && (
              <button
                onClick={requestPermission}
                disabled={isLoading}
                className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 disabled:opacity-50"
              >
                {isLoading ? 'Enabling...' : 'Enable'}
              </button>
            )}
            {isSubscribed && (
              <button
                onClick={unsubscribe}
                disabled={isLoading}
                className="bg-gray-600 text-white px-4 py-2 rounded-md text-sm hover:bg-gray-700 disabled:opacity-50"
              >
                {isLoading ? 'Unsubscribing...' : 'Unsubscribe'}
              </button>
            )}
          </div>
        </div>
        {error && (
          <div className="mt-2 text-sm text-red-600">
            {error}
          </div>
        )}
      </div>
    )
  }

  return null
} 