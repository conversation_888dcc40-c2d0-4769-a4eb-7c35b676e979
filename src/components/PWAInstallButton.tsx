'use client'

import { useState, useEffect } from 'react'
import { getPWASetting } from '@/lib/database'

interface PWAInstallButtonProps {
  className?: string
}

interface DeviceInfo {
  browser: 'chrome' | 'safari' | 'firefox' | 'edge' | 'samsung' | 'unknown'
  os: 'ios' | 'android' | 'windows' | 'macos' | 'unknown'
  isStandalone: boolean
  supportsInstall: boolean
}

export default function PWAInstallButton({ className = '' }: PWAInstallButtonProps) {
  const [mounted, setMounted] = useState(false)
  const [deferredPrompt, setDeferredPrompt] = useState<Event | null>(null)
  const [isInstallable, setIsInstallable] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [isCollapsed, setIsCollapsed] = useState(true)
  const [showInstructions, setShowInstructions] = useState(false)
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>({
    browser: 'unknown',
    os: 'unknown',
    isStandalone: false,
    supportsInstall: false
  })
  const [settings, setSettings] = useState({
    enabled: true,
    position: 'bottom-right',
    style: 'floating',
    text: 'Install App',
    icon: '📱'
  })

  useEffect(() => {
    setMounted(true)
    loadSettings()
    detectDevice()
    
    // Handle PWA installation prompt (Chrome/Edge)
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setDeferredPrompt(e)
      setIsInstallable(true)
    }

    const handleAppInstalled = () => {
      setIsInstalled(true)
      setIsInstallable(false)
      setDeferredPrompt(null)
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  const detectDevice = () => {
    if (typeof window === 'undefined') return;
    
    const userAgent = navigator.userAgent.toLowerCase()
    
    // Detect browser
    let browser: DeviceInfo['browser'] = 'unknown'
    if (userAgent.includes('chrome') && !userAgent.includes('edg')) browser = 'chrome'
    else if (userAgent.includes('safari') && !userAgent.includes('chrome')) browser = 'safari'
    else if (userAgent.includes('firefox')) browser = 'firefox'
    else if (userAgent.includes('edg')) browser = 'edge'
    else if (userAgent.includes('samsung')) browser = 'samsung'

    // Detect OS
    let os: DeviceInfo['os'] = 'unknown'
    if (/iphone|ipad|ipod/.test(userAgent)) os = 'ios'
    else if (/android/.test(userAgent)) os = 'android'
    else if (/windows/.test(userAgent)) os = 'windows'
    else if (/mac/.test(userAgent)) os = 'macos'

    // Check if already in standalone mode
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
      window.matchMedia('(display-mode: fullscreen)').matches ||
      window.matchMedia('(display-mode: minimal-ui)').matches ||
      (window.navigator as any).standalone === true

    // Determine if installation is supported
    const supportsInstall = browser === 'chrome' || browser === 'edge' || browser === 'samsung' || 
      (browser === 'safari' && (os === 'ios' || os === 'macos'))

    setDeviceInfo({
      browser,
      os,
      isStandalone,
      supportsInstall
    })

    setIsInstalled(isStandalone)
  }

  const loadSettings = async () => {
    try {
      const [enabled, position, style, text, icon] = await Promise.all([
        getPWASetting('pwa_install_button_enabled'),
        getPWASetting('pwa_install_button_position'),
        getPWASetting('pwa_install_button_style'),
        getPWASetting('pwa_install_button_text'),
        getPWASetting('pwa_install_button_icon')
      ])

      setSettings({
        enabled: enabled.data?.value || true,
        position: position.data?.value || 'bottom-right',
        style: style.data?.value || 'floating',
        text: text.data?.value || 'Install App',
        icon: icon.data?.value || '📱'
      })
    } catch (error) {
      console.error('Failed to load PWA button settings:', error)
    }
  }

  const handleInstall = async () => {
    if (deferredPrompt) {
      // Chrome/Edge installation
      ;(deferredPrompt as any).prompt()
      const { outcome } = await (deferredPrompt as any).userChoice
      console.log(`User response to install prompt: ${outcome}`)
      
      if (outcome === 'accepted') {
        setIsInstallable(false)
        setDeferredPrompt(null)
      }
    } else {
      // Show device-specific instructions
      setShowInstructions(true)
    }
  }

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed)
  }

  const getInstallInstructions = () => {
    if (deviceInfo.browser === 'safari' && deviceInfo.os === 'ios') {
      return {
        title: 'Install on iPhone/iPad',
        steps: [
          '1. Tap the Share button (□↗) at the bottom of Safari',
          '2. Scroll down and tap "Add to Home Screen"',
          '3. Tap "Add" to install the app'
        ],
        icon: '📱'
      }
    } else if (deviceInfo.browser === 'safari' && deviceInfo.os === 'macos') {
      return {
        title: 'Install on Mac',
        steps: [
          '1. Click "File" in the Safari menu',
          '2. Select "Add to Dock"',
          '3. The app will be added to your Dock'
        ],
        icon: '💻'
      }
    } else if (deviceInfo.browser === 'firefox') {
      return {
        title: 'Install on Firefox',
        steps: [
          '1. Click the address bar',
          '2. Look for the "Install" icon',
          '3. Click "Install" to add to your device'
        ],
        icon: '🦊'
      }
    } else if (deviceInfo.browser === 'chrome' || deviceInfo.browser === 'edge') {
      return {
        title: 'Install App',
        steps: [
          '1. Look for the install prompt',
          '2. Click "Install" when it appears',
          'Or check the address bar for an install icon'
        ],
        icon: '⬇️'
      }
    } else {
      return {
        title: 'Manual Installation',
        steps: [
          '1. Bookmark this page',
          '2. Add it to your home screen/desktop',
          '3. Use your browser\'s "Add to Home Screen" feature'
        ],
        icon: '📌'
      }
    }
  }

  // Don't render anything until component is mounted to prevent hydration mismatch
  if (!mounted) {
    return null
  }

  // Don't render if disabled in settings or already installed
  if (!settings.enabled || isInstalled) {
    return null
  }

  // Don't show if device doesn't support PWA installation
  if (!deviceInfo.supportsInstall) {
    return null
  }

  const getPositionClasses = () => {
    switch (settings.position) {
      case 'bottom-left':
        return 'bottom-4 left-4'
      case 'bottom-right':
        return 'bottom-4 right-4'
      case 'top-left':
        return 'top-4 left-4'
      case 'top-right':
        return 'top-4 right-4'
      case 'bottom-center':
        return 'bottom-4 left-1/2 transform -translate-x-1/2'
      default:
        return 'bottom-4 right-4'
    }
  }

  const getStyleClasses = () => {
    const baseClasses = 'transition-all duration-300 ease-in-out z-50'
    
    switch (settings.style) {
      case 'floating':
        return `${baseClasses} bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl`
      case 'minimal':
        return `${baseClasses} bg-white hover:bg-gray-50 text-gray-700 rounded-lg border border-gray-200 shadow-md hover:shadow-lg`
      case 'gradient':
        return `${baseClasses} bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white rounded-lg shadow-lg hover:shadow-xl`
      default:
        return `${baseClasses} bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl`
    }
  }

  const instructions = getInstallInstructions()

  return (
    <>
      <div className={`fixed ${getPositionClasses()} ${className}`}>
        {isCollapsed ? (
          // Collapsed state - just the icon
          <button
            onClick={toggleCollapse}
            className={`${getStyleClasses()} p-3 group`}
            title="Install App"
          >
            <span className="text-xl group-hover:scale-110 transition-transform">
              {settings.icon}
            </span>
          </button>
        ) : (
          // Expanded state - full button with controls
          <div className={`${getStyleClasses()} p-4 min-w-[200px]`}>
            <div className="flex items-center justify-between mb-3">
              <span className="font-semibold text-sm">PWA Controls</span>
              <button
                onClick={toggleCollapse}
                className="text-xs opacity-70 hover:opacity-100"
              >
                ✕
              </button>
            </div>
            
            <div className="space-y-2">
              {isInstallable ? (
                <button
                  onClick={handleInstall}
                  className="w-full bg-white bg-opacity-20 hover:bg-opacity-30 rounded-md px-3 py-2 text-sm font-medium transition-colors"
                >
                  {settings.icon} {settings.text}
                </button>
              ) : (
                <button
                  onClick={handleInstall}
                  className="w-full bg-white bg-opacity-20 hover:bg-opacity-30 rounded-md px-3 py-2 text-sm font-medium transition-colors"
                >
                  {instructions.icon} Install Instructions
                </button>
              )}
              
              <div className="flex justify-between text-xs opacity-70">
                <a href="/pwa-status" className="hover:opacity-100">
                  Status
                </a>
                <a href="/pwa-test" className="hover:opacity-100">
                  Test
                </a>
              </div>
              
              <div className="text-xs opacity-60 text-center">
                {deviceInfo.browser} • {deviceInfo.os}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Installation Instructions Modal */}
      {showInstructions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-sm w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                {instructions.icon} {instructions.title}
              </h3>
              <button
                onClick={() => setShowInstructions(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <div className="space-y-3">
              {instructions.steps.map((step, index) => (
                <div key={index} className="text-sm text-gray-600">
                  {step}
                </div>
              ))}
            </div>
            
            <div className="mt-6 flex gap-2">
              <button
                onClick={() => setShowInstructions(false)}
                className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 transition-colors"
              >
                Close
              </button>
              <a
                href="/pwa-status"
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors text-center"
              >
                PWA Status
              </a>
            </div>
          </div>
        </div>
      )}
    </>
  )
} 