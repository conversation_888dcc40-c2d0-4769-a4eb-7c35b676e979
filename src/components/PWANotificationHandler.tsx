'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'

export default function PWANotificationHandler() {
  const { data: session } = useSession()
  const [mounted, setMounted] = useState(false)
  const [isStandalone, setIsStandalone] = useState(false)
  const [hasPrompted, setHasPrompted] = useState(false)

  useEffect(() => {
    setMounted(true)
    checkPWAStatus()
  }, [session])

  const checkPWAStatus = async () => {
    if (typeof window === 'undefined') return

    // Check if PWA is in standalone mode (installed)
    const standalone = window.matchMedia('(display-mode: standalone)').matches ||
      window.matchMedia('(display-mode: fullscreen)').matches ||
      window.matchMedia('(display-mode: minimal-ui)').matches ||
      (window.navigator as any).standalone === true

    setIsStandalone(standalone)

    // Clean production logging

    // PWA status checked

    // If PWA is installed, check permission status and act accordingly
    if (standalone && !hasPrompted) {
      if ('Notification' in window) {
        const permission = Notification.permission

        if (permission === 'default') {
          // Auto-prompt for notifications in PWA mode
          setTimeout(() => {
            promptForNotifications()
          }, 1000) // 1 second auto-prompt as requested
        } else if (permission === 'granted') {
          // Set up subscription if already granted
          setTimeout(() => {
            subscribeToNotifications()
          }, 1000)
        }
      }
    }
  }

  const promptForNotifications = async () => {
    if (hasPrompted) return
    setHasPrompted(true)

    console.log('🔔 Prompting for notification permission...')

    try {
      if (!('Notification' in window)) {
        return
      }

      // If already granted, just subscribe
      if (Notification.permission === 'granted') {
        await subscribeToNotifications()
        return
      }

      // If denied, show info message
      if (Notification.permission === 'denied') {
        showNotificationInfo('Notifications are disabled. You can enable them in your browser settings.')
        return
      }

      // Request permission
      const permission = await Notification.requestPermission()

      if (permission === 'granted') {
        await subscribeToNotifications()

        // Show success notification
        new Notification('🎉 Notifications Enabled!', {
          body: 'You will now receive push notifications from this app.',
          icon: '/icons/icon-192x192.png',
          badge: '/icons/icon-72x72.png',
          tag: 'pwa-welcome'
        })
      } else {
        showNotificationInfo('Notifications were not enabled. You can enable them later in settings.')
      }

    } catch (error) {
      console.error('Notification prompt error:', error)
    }
  }

  const subscribeToNotifications = async () => {
    try {
      console.log('🔄 Starting push subscription process...')

      // Register/get service worker
      let registration = await navigator.serviceWorker.getRegistration()
      if (!registration) {
        registration = await navigator.serviceWorker.register('/sw.js')
      }

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready

      // Check if already subscribed
      const existingSubscription = await registration.pushManager.getSubscription()
      if (existingSubscription) {
        return
      }

      // Get VAPID public key
      const vapidResponse = await fetch('/api/pwa/vapid-key')
      const vapidData = await vapidResponse.json()

      if (!vapidData.publicKey) {
        console.error('VAPID key not configured')
        return
      }

      // Subscribe to push notifications
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlB64ToUint8Array(vapidData.publicKey)
      })

      // Save subscription to server
      const response = await fetch('/api/push/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          subscription: {
            endpoint: subscription.endpoint,
            keys: {
              p256dh: btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('p256dh')!))),
              auth: btoa(String.fromCharCode(...new Uint8Array(subscription.getKey('auth')!)))
            }
          },
          userAgent: navigator.userAgent
        })
      })

      const result = await response.json()
      
      if (response.ok) {
        console.log('✅ Push subscription saved successfully:', result)
        showNotificationInfo('🎉 You are now subscribed to push notifications!')
      } else {
        console.error('❌ Failed to save subscription:', result)
      }

    } catch (error) {
      console.error('❌ Push subscription error:', error)
    }
  }

  const showNotificationInfo = (message: string) => {
    // You could show a toast or modal here
    console.log('ℹ️ Info:', message)
  }

  // Helper function to convert VAPID key
  function urlB64ToUint8Array(base64String: string) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding).replace(/-/g, '+').replace(/_/g, '/')
    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)
    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }

  // Clean production PWA experience - no debug UI

  // This component doesn't render anything visible normally
  return null
} 