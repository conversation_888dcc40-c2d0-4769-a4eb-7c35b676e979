import * as Sentry from "@sentry/nextjs";

// Only initialize Sentry if DSN is provided
if (process.env.NEXT_PUBLIC_SENTRY_DSN) {
  Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
    tracesSampleRate: 1.0, // Capture 100% of transactions for performance monitoring
    replaysSessionSampleRate: 0.1, // Capture 10% of sessions for session replay
    replaysOnErrorSampleRate: 1.0, // Capture all sessions if an error occurs
    environment: process.env.NODE_ENV,
    // Set release to a unique identifier, e.g., git commit SHA
    release: process.env.SENTRY_RELEASE || "undefined",
    // integrations are now automatically included
  });
} 